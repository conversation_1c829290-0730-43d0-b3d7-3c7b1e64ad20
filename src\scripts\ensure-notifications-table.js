/**
 * <PERSON><PERSON><PERSON> to ensure the notifications table exists
 * Run this if you're experiencing notification issues
 */

const mysql = require('mysql2/promise');

async function ensureNotificationsTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.MYSQL_HOST || 'localhost',
      user: process.env.MYSQL_USER || 'root',
      password: process.env.MYSQL_PASSWORD || '',
      database: process.env.MYSQL_DATABASE || 'rainbowpaws',
      port: process.env.MYSQL_PORT || 3306
    });

    console.log('Connected to database');

    // Check if notifications table exists
    const [tables] = await connection.execute(`
      SELECT COUNT(*) as count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name = 'notifications'
    `);

    if (tables[0].count === 0) {
      console.log('Creating notifications table...');
      
      // Create notifications table
      await connection.execute(`
        CREATE TABLE notifications (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id INT NOT NULL,
          title VARCHAR(255) NOT NULL,
          message TEXT NOT NULL,
          type ENUM('info', 'success', 'warning', 'error') NOT NULL DEFAULT 'info',
          is_read TINYINT(1) NOT NULL DEFAULT 0,
          link VARCHAR(255) NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_user_id (user_id),
          INDEX idx_is_read (is_read),
          INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `);
      
      console.log('Notifications table created successfully');
    } else {
      console.log('Notifications table already exists');
    }

    // Test insert
    console.log('Testing notification insert...');
    const [result] = await connection.execute(`
      INSERT INTO notifications (user_id, title, message, type)
      VALUES (1, 'Test Notification', 'This is a test notification', 'info')
    `);
    
    console.log('Test notification inserted with ID:', result.insertId);
    
    // Clean up test notification
    await connection.execute('DELETE FROM notifications WHERE id = ?', [result.insertId]);
    console.log('Test notification cleaned up');
    
    console.log('Notifications table is working properly!');

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run if called directly
if (require.main === module) {
  ensureNotificationsTable();
}

module.exports = { ensureNotificationsTable };
