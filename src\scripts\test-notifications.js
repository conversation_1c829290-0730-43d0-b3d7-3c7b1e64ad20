/**
 * Test script for notification functionality
 * Tests both regular and fast notification creation
 */

const { createNotificationFast } = require('../utils/notificationService');

async function testNotifications() {
  console.log('Testing notification functionality...');

  try {
    // Test fast notification creation
    console.log('Testing fast notification creation...');
    const fastResult = await createNotificationFast({
      userId: 1,
      title: 'Test Fast Notification',
      message: 'This is a test of the fast notification system',
      type: 'info',
      link: '/test'
    });

    if (fastResult.success) {
      console.log('✅ Fast notification created successfully:', fastResult.notificationId);
    } else {
      console.log('❌ Fast notification failed:', fastResult.error);
    }

    // Test multiple notifications (simulating admin notifications)
    console.log('Testing multiple notifications...');
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(
        createNotificationFast({
          userId: 1,
          title: `Batch Test ${i + 1}`,
          message: `This is batch notification ${i + 1}`,
          type: 'warning',
          link: '/test'
        })
      );
    }

    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;

    console.log(`Batch test results: ${successful} successful, ${failed} failed`);

    if (failed === 0) {
      console.log('✅ All batch notifications created successfully');
    } else {
      console.log('❌ Some batch notifications failed');
    }

    console.log('Notification testing completed');

  } catch (error) {
    console.error('Error during notification testing:', error);
  }
}

// Run if called directly
if (require.main === module) {
  testNotifications();
}

module.exports = { testNotifications };
